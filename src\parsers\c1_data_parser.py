#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C1故障报警协议解析器
根据C1故障报警协议解析文档将十六进制数据转换为故障状态JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional


class C1DataParser:
    """
    C1故障报警协议解析器类
    用于解析C1命令返回的故障报警状态数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义故障点映射关系
        """
        # 故障点映射表（根据协议文档）
        self.fault_mapping = self._create_fault_mapping()
    
    def _create_fault_mapping(self) -> Dict[str, str]:
        """
        创建故障点映射表（基于协议解析.md第10-537行的准确定义）

        Returns:
            dict: 故障点ID到描述的映射
        """
        fault_map = {}

        # 根据协议解析.md文件的准确定义创建映射表
        fault_definitions = {
            # 24组：电网电压保护和SVG输出电流保护
            "24_0": "电网电压有效值Ⅰ段过压报警",
            "24_1": "电网电压有效值Ⅱ段过压保护",
            "24_2": "电网电压幅值Ⅲ段过压保护",
            "24_3": "电网电压瞬时值过压保护",
            "24_4": "电网电压有效值Ⅰ段欠压报警",
            "24_5": "电网电压有效值Ⅱ段欠压保护",
            "24_6": "电网电压幅值Ⅲ段欠压保护",
            "24_7": "电网电压有效值不平衡保护",
            "24_8": "电网电压缺相保护",
            "24_9": "备用",
            "24_10": "同步信号丢失保护",
            "24_11": "SVG侧霍尔电流传感器故障",
            "24_12": "模拟板采样通道自检故障",
            "24_13": "零序电压超标保护",
            "24_14": "SVG输出电流有效值Ⅰ段过流报警",
            "24_15": "SVG输出电流有效值Ⅱ段过流保护",

            # 25组：SVG电流保护和系统故障
            "25_0": "SVG输出电流瞬时值过流1(Vpp)",
            "25_1": "SVG输出电流硬件过流(HW)",
            "25_2": "SVG输出电流缺相保护",
            "25_3": "输出电流指令限幅",
            "25_4": "SVG瞬时电流过流（CT检测方式）故障",
            "25_5": "PT故障",
            "25_6": "SVG零序电流故障",
            "25_7": "功率单元状态一般性故障(SW)",
            "25_8": "功率单元UDC过压保护(SW)",
            "25_9": "功率单元UDC不平衡保护(SW)",
            "25_10": "功率单元硬件保护(HW)",
            "25_11": "单元自检故障",
            "25_12": "单元状态不一致",
            "25_13": "RS485通信超时故障",
            "25_14": "RS485通信校验故障",
            "25_15": "DRAM读超时故障",

            # 26组：系统硬件故障
            "26_0": "DRAM写参数故障",
            "26_1": "功率单元故障",
            "26_2": "WDI看门狗复位",
            "26_3": "充电超时故障",
            "26_4": "行程开关故障",
            "26_5": "充电接触器K1不吸合",
            "26_6": "充电接触器K1不分开",
            "26_7": "断路器QF1不吸合",
            "26_8": "断路器QF1不分开",
            "26_9": "CAN发送超时",
            "26_10": "PWM板通信故障",
            "26_11": "水冷系统电源故障",
            "26_12": "读铁电参数错误",
            "26_13": "变压器超温报警",
            "26_14": "变压器超温跳闸",
            "26_15": "变压器轻瓦斯报警",

            # 27组：变压器保护和水冷系统故障
            "27_0": "变压器重瓦斯跳闸",
            "27_1": "变压器压力报警",
            "27_2": "变压器压力跳闸",
            "27_3": "风机缺相保护",
            "27_4": "单元短路故障",
            "27_5": "瞬时过流2跳闸",
            "27_6": "电网低电压1保护",
            "27_7": "电网低电压2保护",
            "27_8": "自动恢复失败次数超限",
            "27_9": "故障复位超时",
            "27_10": "PWM通讯故障",
            "27_11": "水冷系统运行状态异常",
            "27_12": "水冷系统综合故障",
            "27_13": "水冷系统报警",
            "27_14": "水冷系统请求停止",
            "27_15": "高速光纤通信故障",
        }

        # 添加A相功率单元故障定义（28-33组）
        a_phase_definitions = {}
        for unit in range(1, 7):  # A1-A6
            base_group = 27 + unit  # 28-33
            a_phase_definitions.update({
                f"{base_group}_0": f"A{unit}相控CPU板ROM参数故障",
                f"{base_group}_1": f"A{unit}相功率单元未启动",
                f"{base_group}_2": f"A{unit}相功率单元未停止",
                f"{base_group}_3": f"A{unit}相相控ROM参数故障",
                f"{base_group}_4": f"A{unit}相控PWM板与CPU板背板总线测试故障",
                f"{base_group}_5": f"A{unit}相控PWM板与CPU板级数不一致",
                f"{base_group}_6": f"A{unit}相功率单元CPLD程序版本不匹配",
                f"{base_group}_7": "备用",
                f"{base_group}_8": f"A{unit}相控PWM板5V电源故障",
                f"{base_group}_9": f"A{unit}相PWM板RX光纤断",
                f"{base_group}_10": f"A{unit}相PWM板硬件保护",
                f"{base_group}_11": f"A{unit}相PWM板FLT光纤断",
                f"{base_group}_12": f"A{unit}相PWM板控制信号中断",
                f"{base_group}_13": f"A{unit}相PWM板LOCK光纤断",
                f"{base_group}_14": f"A{unit}相PWM板SYNC光纤断",
                f"{base_group}_15": f"A{unit}相PWM板TX光纤断",
            })

        # 添加三相PWM板硬件故障（34组）
        fault_definitions.update({
            "34_0": "A相PWM板硬件故障",
            "34_1": "B相PWM板硬件故障",
            "34_2": "C相PWM板硬件故障",
            "34_3": "A相输出电流硬件过流保护",
            "34_4": "B相输出电流硬件过流保护",
            "34_5": "C相输出电流硬件过流保护",
            "34_6": "A相PWM板背板总线测试故障",
            "34_7": "B相PWM板背板总线测试故障",
            "34_8": "C相PWM板背板总线测试故障",
            "34_9": "A相功率单元CPLD程序版本不匹配",
            "34_10": "B相功率单元CPLD程序版本不匹配",
            "34_11": "C相功率单元CPLD程序版本不匹配",
            "34_12": "备用",
            "34_13": "备用",
            "34_14": "备用",
            "34_15": "备用",
        })

        # 添加A/B/C相PWM板故障（35-37组）
        for phase_idx, phase_name in enumerate(['A', 'B', 'C']):
            base_group = 35 + phase_idx
            fault_definitions.update({
                f"{base_group}_0": f"{phase_name}相PWM板ROM参数与CPU板参数不一致故障",
                f"{base_group}_1": f"{phase_name}相功率单元未启动",
                f"{base_group}_2": f"{phase_name}相功率单元未停止",
                f"{base_group}_3": f"{phase_name}相PWM板ROM参数故障",
                f"{base_group}_4": f"{phase_name}相PWM板与CPU板通信故障",
                f"{base_group}_5": f"{phase_name}相PWM板参数下载校验错误",
                f"{base_group}_6": f"{phase_name}相PWM与主控CPU板读超时故障",
                f"{base_group}_7": f"{phase_name}相PWM板5V电源故障",
                f"{base_group}_8": f"{phase_name}相PWM板看门狗超时故障",
                f"{base_group}_9": f"{phase_name}相PWM板NIOS软核读写超时故障",
                f"{base_group}_10": f"{phase_name}相PWM板硬件故障",
                f"{base_group}_11": "备用",
                f"{base_group}_12": "备用",
                f"{base_group}_13": "备用",
                f"{base_group}_14": "备用",
                f"{base_group}_15": "备用",
            })

        # 添加CPU铁电故障（38组）
        fault_definitions.update({
            "38_0": "CPU铁电故障",
        })
        for i in range(1, 16):
            fault_definitions[f"38_{i}"] = "备用"

        # 添加系统运行状态（39组）
        fault_definitions.update({
            "39_0": "起始状态",
            "39_1": "充电",
            "39_2": "单元自检",
            "39_3": "复位",
            "39_4": "就绪",
            "39_5": "运行",
            "39_6": "故障",
            "39_7": "高压",
            "39_8": "开机自检",
            "39_9": "备用",
            "39_10": "合高压等待",
            "39_11": "故障2",
            "39_12": "复位2",
            "39_13": "备用",
            "39_14": "备用",
            "39_15": "备用",
        })

        # 添加B相功率单元故障定义（40-45组）
        b_phase_definitions = {}
        for unit in range(1, 7):  # B1-B6
            base_group = 39 + unit  # 40-45
            b_phase_definitions.update({
                f"{base_group}_0": f"B{unit}相控CPU板ROM参数故障",
                f"{base_group}_1": f"B{unit}相功率单元未启动",
                f"{base_group}_2": f"B{unit}相功率单元未停止",
                f"{base_group}_3": f"B{unit}相相控ROM参数故障",
                f"{base_group}_4": f"B{unit}相控PWM板与CPU板背板总线测试故障",
                f"{base_group}_5": f"B{unit}相控PWM板与CPU板级数不一致",
                f"{base_group}_6": f"B{unit}相功率单元CPLD程序版本不匹配",
                f"{base_group}_7": "备用",
                f"{base_group}_8": f"B{unit}相控PWM板5V电源故障",
                f"{base_group}_9": f"B{unit}相PWM板RX光纤断",
                f"{base_group}_10": f"B{unit}相PWM板硬件保护",
                f"{base_group}_11": f"B{unit}相PWM板FLT光纤断",
                f"{base_group}_12": f"B{unit}相PWM板控制信号中断",
                f"{base_group}_13": f"B{unit}相PWM板LOCK光纤断",
                f"{base_group}_14": f"B{unit}相PWM板SYNC光纤断",
                f"{base_group}_15": f"B{unit}相PWM板TX光纤断",
            })

        # 添加C相功率单元故障定义（46-51组）
        c_phase_definitions = {}
        for unit in range(1, 7):  # C1-C6
            base_group = 45 + unit  # 46-51
            c_phase_definitions.update({
                f"{base_group}_0": f"C{unit}相控CPU板ROM参数故障",
                f"{base_group}_1": f"C{unit}相功率单元未启动",
                f"{base_group}_2": f"C{unit}相功率单元未停止",
                f"{base_group}_3": f"C{unit}相相控ROM参数故障",
                f"{base_group}_4": f"C{unit}相控PWM板与CPU板背板总线测试故障",
                f"{base_group}_5": f"C{unit}相控PWM板与CPU板级数不一致",
                f"{base_group}_6": f"C{unit}相功率单元CPLD程序版本不匹配",
                f"{base_group}_7": "备用",
                f"{base_group}_8": f"C{unit}相控PWM板5V电源故障",
                f"{base_group}_9": f"C{unit}相PWM板RX光纤断",
                f"{base_group}_10": f"C{unit}相PWM板硬件保护",
                f"{base_group}_11": f"C{unit}相PWM板FLT光纤断",
                f"{base_group}_12": f"C{unit}相PWM板控制信号中断",
                f"{base_group}_13": f"C{unit}相PWM板LOCK光纤断",
                f"{base_group}_14": f"C{unit}相PWM板SYNC光纤断",
                f"{base_group}_15": f"C{unit}相PWM板TX光纤断",
            })

        # 添加备用故障点（52-53组）
        for group in [52, 53]:
            for bit in range(16):
                fault_definitions[f"{group}_{bit}"] = "备用"

        # 添加并联运行模式和控制状态（54-56组）
        fault_definitions.update({
            "54_0": "并联运行模式启动",
            "54_1": "并联运行模式停止",
            "54_2": "本地为主机",
            "54_3": "本地为从机",
            "54_4": "主从机配置错误",
            "54_5": "母联状态错误",
            "54_6": "本地并联模式配置错误",
            "54_7": "本地远程控制策略不一致",
            "55_0": "远程为主机",
            "55_1": "远程为从机",
            "55_2": "远程启动",
            "55_3": "远程停止",
            "56_0": "无功补偿模式启动",
            "56_1": "谐波补偿模式启动",
            "56_2": "综合补偿模式启动",
        })

        # 添加剩余备用位
        for group in [54, 55, 56]:
            for bit in range(16):
                if f"{group}_{bit}" not in fault_definitions:
                    fault_definitions[f"{group}_{bit}"] = "备用"

        # 添加所有定义的故障点
        fault_map.update(fault_definitions)
        fault_map.update(a_phase_definitions)
        fault_map.update(b_phase_definitions)
        fault_map.update(c_phase_definitions)

        # 对于未定义的故障点，使用通用描述
        for group in range(24, 57):  # 24到56
            for bit in range(16):
                fault_id = f"{group}_{bit}"
                if fault_id not in fault_map:
                    fault_map[fault_id] = f"故障点{group}_{bit}"

        return fault_map
    
    def extract_data_fields(self, raw_hex: str) -> Dict[str, Any]:
        """
        从raw_hex数据中提取C1命令的数据字段
        
        Args:
            raw_hex: 原始十六进制字符串
            
        Returns:
            dict: 包含data_hex和data_hex_formatted的字典
        """
        try:
            # 移除空格并转换为字节
            hex_clean = raw_hex.replace(' ', '').upper()
            if len(hex_clean) % 2 != 0:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '十六进制数据长度不是偶数'
                }
            
            data = bytes.fromhex(hex_clean)
            
            # 验证帧头和命令码
            if len(data) < 15:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '数据长度不足，无法解析C1命令'
                }
            
            # 检查帧头 EB 90 01 01
            if data[:4] != bytes([0xEB, 0x90, 0x01, 0x01]):
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '帧头不匹配，不是有效的协议帧'
                }
            
            # 检查命令码 C1
            if data[4] != 0xC1:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '命令码不是C1'
                }
            
            # 获取数据长度（第5字节）
            data_length = data[5]
            if data_length != 0x44:  # C1命令数据长度应为68字节(0x44)
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f'C1命令数据长度错误，期望68字节，实际{data_length}字节'
                }
            
            # C1命令的数据从第9字节开始（帧头4 + 命令1 + 数据长度1 + 空字节占位3 = 9）
            data_start = 9
            data_end = data_start + data_length
            
            if len(data) < data_end:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f'数据不完整，期望{data_end}字节，实际{len(data)}字节'
                }
            
            data_bytes = data[data_start:data_end]
            
            # 生成data_hex字段（原始十六进制数据）
            data_hex = data_bytes.hex().upper()
            
            # 生成data_hex_formatted字段（按2字节一组分为16位值，小端序）
            data_hex_formatted = []
            if len(data_bytes) % 2 == 0:
                # C1命令：按2字节一组分为16位值（小端序）
                for i in range(0, len(data_bytes), 2):
                    value = int.from_bytes(data_bytes[i:i+2], byteorder='little')
                    data_hex_formatted.append(f'0x{value:04X}')
            else:
                # 如果字节数为奇数，按字节显示
                data_hex_formatted = [f'0x{b:02X}' for b in data_bytes]
            
            return {
                'data_hex': data_hex,
                'data_hex_formatted': data_hex_formatted,
                'success': True
            }
            
        except ValueError as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': f'十六进制数据格式错误: {e}'
            }
        except Exception as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': f'数据提取失败: {e}'
            }
    
    def parse_fault_from_formatted_data(self, data_hex_formatted: List[str]) -> Dict[str, Any]:
        """
        从格式化的十六进制数据中解析故障状态
        
        Args:
            data_hex_formatted: 格式化的十六进制数据列表
            
        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if len(data_hex_formatted) != 34:
                return {
                    'faults': [],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f'C1命令数据应包含34个16位值，实际{len(data_hex_formatted)}个'
                }
            
            faults = []
            active_fault_count = 0
            
            # 解析34个16位数据
            for group_index, hex_value in enumerate(data_hex_formatted):
                try:
                    # 转换为整数
                    value = int(hex_value, 16)
                    
                    # 计算起始点编号（24 + group_index）
                    start_point = 24 + group_index
                    
                    # 解析16位中的每一位
                    for bit_index in range(16):
                        fault_id = f"{start_point}_{bit_index}"
                        bit_value = (value >> bit_index) & 1
                        
                        # 获取故障描述
                        fault_name = self.fault_mapping.get(fault_id, f"备份点{fault_id}")
                        
                        fault_info = {
                            'id': fault_id,
                            'name': fault_name,
                            'value': bit_value,
                            'hex_source': hex_value
                        }
                        
                        faults.append(fault_info)
                        
                        if bit_value == 1:
                            active_fault_count += 1
                            
                except ValueError as e:
                    return {
                        'faults': [],
                        'total_fault_points': 0,
                        'active_faults': 0,
                        'success': False,
                        'error': f'无法解析十六进制值 {hex_value}: {e}'
                    }
            
            return {
                'faults': faults,
                'total_fault_points': len(faults),
                'active_faults': active_fault_count,
                'success': True
            }
            
        except Exception as e:
            return {
                'faults': [],
                'total_fault_points': 0,
                'active_faults': 0,
                'success': False,
                'error': f'故障解析失败: {e}'
            }
    
    def parse_fault_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析C1命令的完整响应数据
        整合数据提取和故障解析功能
        
        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            
        Returns:
            dict: 包含解析结果的字典
        """
        try:
            # 第一步：提取data_hex和data_hex_formatted字段
            extract_result = self.extract_data_fields(raw_hex)
            if not extract_result['success']:
                return {
                    'command': 'C1',
                    'faults': [],
                    'raw_data': raw_hex,
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f"数据提取失败: {extract_result.get('error', '未知错误')}"
                }
            
            # 第二步：基于data_hex_formatted进行故障解析
            parse_result = self.parse_fault_from_formatted_data(extract_result['data_hex_formatted'])
            if not parse_result['success']:
                return {
                    'command': 'C1',
                    'faults': [],
                    'raw_data': raw_hex,
                    'data_hex': extract_result['data_hex'],
                    'data_hex_formatted': extract_result['data_hex_formatted'],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f"故障解析失败: {parse_result.get('error', '未知错误')}"
                }
            
            # 成功解析
            return {
                'command': 'C1',
                'faults': parse_result['faults'],
                'raw_data': raw_hex,
                'data_hex': extract_result['data_hex'],
                'data_hex_formatted': extract_result['data_hex_formatted'],
                'total_fault_points': parse_result['total_fault_points'],
                'active_faults': parse_result['active_faults'],
                'success': True
            }
            
        except Exception as e:
            return {
                'command': 'C1',
                'faults': [],
                'raw_data': raw_hex,
                'data_hex': '',
                'data_hex_formatted': [],
                'total_fault_points': 0,
                'active_faults': 0,
                'success': False,
                'error': f"解析过程异常: {e}"
            }

    def parse_c1_data(self, raw_hex: str, timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        解析C1命令的raw_hex数据
        整合数据提取和故障解析功能，生成MQTT消息格式

        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含故障状态的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if not raw_hex:
            raise ValueError("C1命令数据格式错误，数据不能为空")

        try:
            # 解析数据（使用新的整合方法）
            parsed_data = self.parse_fault_response(raw_hex)

            # 检查解析是否成功
            if not parsed_data.get('success', False):
                error_msg = parsed_data.get('error', '未知解析错误')
                raise ValueError(f"C1命令数据解析失败: {error_msg}")

            # 转换为MQTT格式
            for fault in parsed_data['faults']:
                fault_item = {
                    "id": fault['id'],
                    "name": fault['name'],
                    "ts": timestamp,
                    "value": fault['value'],
                    "hex_value": fault['hex_source']
                }
                result.append(fault_item)

            return result

        except Exception as e:
            raise ValueError(f"C1命令数据解析异常: {e}")

    def parse_c1_data_to_json(self, raw_hex: str, timestamp: Optional[str] = None,
                             output_file: Optional[str] = None) -> str:
        """
        解析C1命令数据并输出为JSON格式

        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间
            output_file: 输出文件路径，如果提供则保存到文件

        Returns:
            JSON格式的字符串
        """
        try:
            # 解析数据
            parsed_data = self.parse_c1_data(raw_hex, timestamp)

            # 转换为JSON字符串
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)

            # 如果指定了输出文件，则保存
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(json_str)
                print(f"C1解析结果已保存到: {output_file}")

            return json_str

        except Exception as e:
            error_msg = f"C1数据解析到JSON失败: {e}"
            print(error_msg)
            return json.dumps({"error": error_msg}, ensure_ascii=False, indent=4)


def main():
    """
    主函数：从realtime_data.json读取C1命令数据并进行解析
    """
    realtime_data_file = "data/realtime_data.json"

    try:
        # 读取realtime_data.json
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        parser = C1DataParser()

        print("C1故障报警数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)

        # 查找C1命令数据
        c1_data_found = False
        for item in data:
            if item.get('command') == 'C1' and item.get('success'):
                c1_data_found = True

                print(f"找到C1命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")

                # 如果存在data_hex_formatted，显示它
                if item.get('data_hex_formatted'):
                    print(f"已有格式化数据: {item.get('data_hex_formatted')}")

                print()

                # 使用raw_hex进行完整解析（包含数据提取和故障解析）
                raw_hex = item.get('raw_hex', '')
                if not raw_hex:
                    print("警告: raw_hex数据为空，跳过解析")
                    continue

                # 演示数据提取功能
                print("步骤1: 数据提取")
                extract_result = parser.extract_data_fields(raw_hex)
                if extract_result['success']:
                    print(f"  data_hex: {extract_result['data_hex']}")
                    print(f"  data_hex_formatted: {extract_result['data_hex_formatted'][:5]}...（显示前5个）")
                else:
                    print(f"  数据提取失败: {extract_result.get('error')}")
                    continue

                print()

                # 演示基于格式化数据的故障解析
                print("步骤2: 故障解析")
                fault_result = parser.parse_fault_from_formatted_data(extract_result['data_hex_formatted'])
                if fault_result['success']:
                    print(f"  解析成功，共{fault_result['total_fault_points']}个故障点")
                    print(f"  活跃故障: {fault_result['active_faults']}个")
                    if fault_result['active_faults'] > 0:
                        print("  活跃故障列表:")
                        for fault in fault_result['faults']:
                            if fault['value'] == 1:
                                print(f"    {fault['id']}: {fault['name']}")
                else:
                    print(f"  故障解析失败: {fault_result.get('error')}")
                    continue

                print()

                # 生成最终的MQTT格式数据
                print("步骤3: 生成MQTT格式数据")
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                # 只调用一次parse_c1_data_to_json，同时完成显示和保存
                output_file = "data/c1_parsed_data.json"
                json_result = parser.parse_c1_data_to_json(
                    raw_hex,
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                print("MQTT格式解析结果（显示前3个故障点）:")
                parsed_list = json.loads(json_result)
                for i, fault in enumerate(parsed_list[:3]):
                    print(f"  {fault}")
                if len(parsed_list) > 3:
                    print(f"  ... 还有{len(parsed_list) - 3}个故障点")

                break  # 只处理第一个找到的C1数据

        if not c1_data_found:
            print("在realtime_data.json中未找到有效的C1命令数据")
            print("请确保文件中包含command='C1'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()
